import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/product/card';
import { Button } from '@/components/ui/product/button';

import { ChevronLeft, ChevronRight, Save, Check, HelpCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useApi } from "@/hooks/useApi";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

// Import your existing step components
import StepProductDetails from '@/components/ProductWizard/steps/StepProductDetails';
import StepDetailsDescription from '@/components/ProductWizard/steps/StepDetailsDescription';
import StepProductMedia from '@/components/ProductWizard/steps/StepProductMedia';
import StepPricingInventory from '@/components/ProductWizard/steps/StepPricingInventory';
import StepComplianceFulfillment from '@/components/ProductWizard/steps/StepComplianceFulfillment';
import StepSEOFAQs from '@/components/ProductWizard/steps/StepSEOFAQs';
import StepReview from '@/components/ProductWizard/steps/StepReview';

// Define the product data interface
const STEPS = [
  { id: 1, title: 'Classification', component: StepProductDetails },
  { id: 2, title: 'Product Details', component: StepDetailsDescription },
  { id: 3, title: 'Product Media', component: StepProductMedia },
  { id: 4, title: 'Pricing & Inventory', component: StepPricingInventory },
  { id: 5, title: 'Compliance & Fulfillment', component: StepComplianceFulfillment },
  { id: 6, title: 'SEO & FAQs', component: StepSEOFAQs },
  { id: 7, title: 'Review & Submit', component: StepReview },
];

const ProductWizardPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const isEditMode = Boolean(id);
  const { toast } = useToast();
  const { fetchData, postMutation, putMutation } = useApi();

  const [currentStep, setCurrentStep] = useState(1);
  const [layoutMode, setLayoutMode] = useState('horizontal');
  const [loading, setLoading] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [validationErrors] = useState({});
  const [showValidation, setShowValidation] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Step descriptions for tooltips
  const stepDescriptions = {
    1: "Classify your product by category, brand, and define unique identifiers like SKU and barcode",
    2: "Set product titles, descriptions, and key product information",
    3: "Upload up to 6 product images. Drag to reorder, set one as primary, and add alt text for accessibility",
    4: "Configure pricing, inventory, and product variants",
    5: "Set compliance information, dietary certifications, and fulfillment logistics",
    6: "Configure SEO metadata and frequently asked questions for better discoverability",
    7: "Review all your product information before submitting for approval"
  };

  const [productData, setProductData] = useState({
    category_id: '',
    sub_category_id: '',
    class_id: '',
    sub_class_id: '',
    brand_id: '',
    vendor_sku: '',
    barcode: '',
    system_sku: '',
    title_en: '',
    title_ar: '',
    short_name: '',
    model_number: '',
    short_description_en: '',
    short_description_ar: '',
    description_en: '',
    description_ar: '',
    key_ingredients: '',
    usage_instructions: '',
    user_group: '',
    net_weight: '',
    net_weight_unit: '',
    formulation: '',
    servings: 0,
    flavour: '',
    media: [],
    is_variant: false,
    regular_price: 0,
    offer_price: 0,
    discount_start_date: '',
    discount_end_date: '',
    vat_tax: 0,
    approx_commission: 0,
    stock: 0,
    reserved: 0,
    threshold: 0,
    stock_status: 'in_stock',
    warehouse_id: '',
    attributes: [],
    variants: [],
    dietary_needs: '',
    is_vegan: false,
    is_vegetarian: false,
    is_halal: false,
    allergen_info: '',
    storage_conditions: '',
    country_of_origin: '',
    bbe_date: '',
    regulatory_product_registration: '',
    vat_tax_utl: '',
    meta_title_en: '',
    meta_title_ar: '',
    meta_description_en: '',
    meta_description_ar: '',
    keywords_en: '',
    keywords_ar: '',
    slug: '',
    // Fulfillment fields
    mode: '',
    is_returnable: false,
    collection_point: '',
    shipping_time: '',
    shipping_fee: 0,
    package_length: 0,
    package_width: 0,
    package_height: 0,
    package_weight: 0,
    // FAQ fields
    faqs: [],
  });

  // Fetch existing product data if editing
  const {
    data: existingProductData,
    isLoading: productLoading,
    isError: productError,
  } = isEditMode ? fetchData(`admin/products/${id}`) : { data: null, isLoading: false, isError: false };

  // Initialize form data when product loads
  useEffect(() => {
    if (existingProductData?.data && isEditMode) {
      const product = existingProductData.data;
      setProductData(product);
      // Mark steps as completed based on available data
      const completed = new Set();
      if (product.title_en) completed.add(1);
      if (product.description_en) completed.add(2);
      if (product.media && product.media.length > 0) completed.add(3);
      if (product.regular_price) completed.add(4);
      if (product.mode) completed.add(5);
      if (product.meta_title_en || product.faqs?.length > 0) completed.add(6);
      setCompletedSteps(completed);
    }
  }, [existingProductData, isEditMode]);

  // Auto-generate system SKU when vendor SKU changes
  useEffect(() => {
    if (productData.vendor_sku) {
      const timestamp = Date.now().toString().slice(-6);
      setProductData(prev => ({
        ...prev,
        system_sku: `SYS-${productData.vendor_sku}-${timestamp}`
      }));
    }
  }, [productData.vendor_sku]);

  // Auto-generate vendor SKU from barcode if empty
  useEffect(() => {
    if (!productData.vendor_sku && productData.barcode && productData.barcode.length >= 6) {
      const generatedSku = productData.barcode.slice(-6);
      setProductData(prev => ({
        ...prev,
        vendor_sku: `SKU-${generatedSku}`
      }));
    }
  }, [productData.barcode, productData.vendor_sku]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimeout = setTimeout(() => {
      if (Object.values(productData).some(value => value !== '' && value !== 0 && value !== false)) {
        setIsAutoSaving(true);
        setTimeout(() => {
          setIsAutoSaving(false);
        }, 1000);
      }
    }, 3000);

    return () => clearTimeout(autoSaveTimeout);
  }, [productData]);

  // Hide validation when user fills required fields
  useEffect(() => {
    if (showValidation && validateCurrentStep(currentStep)) {
      setShowValidation(false);
    }
  }, [productData, currentStep, showValidation]);

  // Close tooltip when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showTooltip && !event.target.closest('.tooltip-container')) {
        setShowTooltip(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTooltip]);

  const calculateProgress = () => {
    const totalFields = Object.keys(productData).length - 3;
    const filledFields = Object.entries(productData).filter(([key, value]) => {
      if (['media', 'attributes', 'variants', 'faqs'].includes(key)) return false;
      if (key === 'system_sku') return false;
      return value !== '' && value !== 0 && value !== false;
    }).length;

    return Math.round((filledFields / totalFields) * 100);
  };

  const validateCurrentStep = (stepId) => {
    switch (stepId) {
      case 1: // Classification & Identity - MANDATORY: Product Category, Sub Category, BAR CODE, Brand Name
        return !!(
          productData.category_id &&
          productData.sub_category_id &&
          productData.barcode &&
          productData.brand_id
        );
      case 2: // Details & Description - MANDATORY: Product Name/Title[en], Full Product Description[en], Usage Instructions, User Group, Size/Net Wt., Size Units
        return !!(
          productData.title_en &&
          productData.description_en &&
          productData.usage_instructions &&
          productData.user_group &&
          (productData.size || productData.net_wt) &&
          productData.size_units
        );
      case 3: // Product Media - Optional
        return true;
      case 4: // Pricing & Inventory - MANDATORY: Regular Price, Offer Price, Quantity
        return !!(
          productData.regular_price > 0 &&
          productData.offer_price >= 0 &&
          productData.stock >= 0
        );
      case 5: // Compliance & Fulfillment - MANDATORY: Vegan, Vegetarian, Halal, Allergen Information, Mode, Collection Point, Returnable
        return !!(
          (productData.is_vegan !== undefined && productData.is_vegan !== null) &&
          (productData.is_vegetarian !== undefined && productData.is_vegetarian !== null) &&
          (productData.is_halal !== undefined && productData.is_halal !== null) &&
          productData.allergen_info &&
          productData.mode &&
          productData.collection_point &&
          (productData.is_returnable !== undefined && productData.is_returnable !== null)
        );
      case 6: // SEO & FAQs - Optional
        return true;
      default:
        return true;
    }
  };

  const handleNext = () => {
    // Show validation for the current step
    setShowValidation(true);
    setShowTooltip(false); // Hide tooltip when navigating

    if (validateCurrentStep(currentStep)) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setShowValidation(false); // Hide validation when moving to next step
      const nextStepIndex = STEPS.findIndex(s => s.id === currentStep) + 1;
      if (nextStepIndex < STEPS.length) {
        setCurrentStep(STEPS[nextStepIndex].id);
      }
    } else {
      toast({
        title: "Incomplete Step",
        description: "Please fill in all required fields before proceeding.",
        variant: "destructive",
      });
    }
  };

  const handlePrevious = () => {
    setShowTooltip(false); // Hide tooltip when navigating
    const currentIndex = STEPS.findIndex(s => s.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(STEPS[currentIndex - 1].id);
    }
  };

  const handleStepClick = (stepId) => {
    setCurrentStep(stepId);
    setShowValidation(false); // Hide validation when changing steps
    setShowTooltip(false); // Hide tooltip when changing steps
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      if (isEditMode) {
        await putMutation.mutateAsync({
          endpoint: `admin/products/${id}/draft`,
          data: { ...productData, status: 'draft' }
        });
      } else {
        await postMutation.mutateAsync({
          endpoint: "admin/products/draft",
          data: { ...productData, status: 'draft' }
        });
      }
      toast({
        title: "Draft Saved",
        description: "Your product has been saved as a draft.",
      });
      navigate("/products/all");
    } catch (error) {
      console.error("Save draft failed:", error);
      toast({
        title: "Error",
        description: "Failed to save draft: " + (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitForApproval = async () => {
    if (calculateProgress() < 80) {
      toast({
        title: "Incomplete Product",
        description: "Please complete at least 80% of the form before submitting.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      if (isEditMode) {
        await putMutation.mutateAsync({
          endpoint: `admin/products/${id}`,
          data: { ...productData, status: 'pending_approval' }
        });
      } else {
        await postMutation.mutateAsync({
          endpoint: "admin/products",
          data: { ...productData, status: 'pending_approval' }
        });
      }
      toast({
        title: "Submitted for Approval",
        description: "Your product has been submitted for review.",
      });
      navigate("/products/all");
    } catch (error) {
      console.error("Submit failed:", error);
      toast({
        title: "Error",
        description: "Failed to submit product: " + (error?.response?.data?.message || error.message),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentStepComponent = () => {
    const currentStepData = STEPS.find(s => s.id === currentStep);
    return currentStepData?.component || STEPS[0].component;
  };

  const CurrentStepComponent = getCurrentStepComponent();

  if (productLoading) {
    return <LoadingSpinner size={64} overlay />;
  }

  if (productError) {
    return (
      <div className="max-w-7xl mx-auto relative pt-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-red-800">Error Loading Product</h3>
          <p className="text-red-600 mt-2">
            Failed to load product data. Please try again or contact support.
          </p>
          <Button
            variant="outline"
            onClick={() => navigate("/products/all")}
            className="mt-4"
          >
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto relative pt-6">
      {(loading || isAutoSaving) && <LoadingSpinner size={64} overlay />}
        {/* Compact Header */}
        <div className="mb-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h1 className="text-xl font-semibold text-gray-900">
                  {isEditMode ? t('products.editProduct') : t('products.createProduct')}
                </h1>
                <div className="hidden sm:flex items-center gap-2 text-sm text-gray-500">
                  <span>{t('products.step')} {currentStep}</span>
                  <span>/</span>
                  <span>{STEPS.length}</span>
                </div>
              </div>
              <div className="flex items-center gap-4">
                {isAutoSaving && (
                  <div className="flex items-center gap-1 text-xs text-blue-600">
                    <Save className="w-3 h-3 animate-pulse" />
                    <span className="hidden sm:inline">{t('products.autoSaving')}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">{calculateProgress()}%</div>
                    <div className="text-xs text-gray-500">{completedSteps.size}/{STEPS.length}</div>
                  </div>
                  <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 transition-all duration-300"
                      style={{ width: `${calculateProgress()}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Step Indicator */}
        <div className="mb-4">
          <div className="bg-white rounded-lg border border-gray-200 p-3">
            {/* Desktop and Tablet View */}
            <div className="hidden sm:block">
              <div className="flex items-center justify-between">
                {STEPS.map((step, index) => (
                  <div key={step.id} className="flex items-center flex-1">
                    <div className="flex items-center">
                      <div
                        onClick={() => handleStepClick(step.id)}
                        className={`flex items-center justify-center w-8 h-8 rounded-full cursor-pointer transition-all duration-200 ${
                          currentStep === step.id
                            ? 'bg-blue-600 text-white'
                            : completedSteps.has(step.id)
                            ? 'bg-green-600 text-white'
                            : 'bg-gray-200 text-gray-500 hover:bg-gray-300'
                        }`}
                      >
                        {completedSteps.has(step.id) ? (
                          <Check className="w-4 h-4" />
                        ) : (
                          <span className="text-xs font-semibold">{index + 1}</span>
                        )}
                      </div>
                      <div className="ml-2 hidden lg:block">
                        <p className={`text-xs font-medium ${
                          currentStep === step.id ? 'text-blue-600' : 'text-gray-600'
                        }`}>
                          {step.title}
                        </p>
                      </div>
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className="flex-1 mx-3">
                        <div className={`h-0.5 rounded-full transition-all duration-300 ${
                          completedSteps.has(step.id) ? 'bg-green-400' : 'bg-gray-200'
                        }`} />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile View */}
            <div className="sm:hidden">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full ${
                      currentStep === STEPS.find(s => s.id === currentStep)?.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-500'
                    }`}
                  >
                    <span className="text-xs font-semibold">{currentStep}</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {STEPS.find(s => s.id === currentStep)?.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {t('products.step')} {currentStep} {t('products.of')} {STEPS.length}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  {STEPS.map((step) => (
                    <div
                      key={step.id}
                      onClick={() => handleStepClick(step.id)}
                      className={`w-2 h-2 rounded-full cursor-pointer transition-all duration-200 ${
                        currentStep === step.id
                          ? 'bg-blue-600'
                          : completedSteps.has(step.id)
                          ? 'bg-green-600'
                          : 'bg-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Card className="min-h-[600px]">
          <CardHeader className="border-b border-gray-200 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg font-semibold">
                  {STEPS.find(s => s.id === currentStep)?.title}
                </CardTitle>
                <div className="relative tooltip-container">
                  <button
                    onClick={() => setShowTooltip(!showTooltip)}
                    className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                    aria-label="Step information"
                    aria-expanded={showTooltip}
                    aria-describedby={showTooltip ? 'step-tooltip' : undefined}
                  >
                    <HelpCircle className="w-3 h-3 text-gray-500" />
                  </button>
                  {showTooltip && (
                    <div
                      id="step-tooltip"
                      className="absolute left-0 top-6 z-[9999] w-80 max-w-[calc(100vw-2rem)] p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg sm:left-0 sm:w-80"
                      role="tooltip"
                    >
                      <div className="relative">
                        {stepDescriptions[currentStep]}
                        <div className="absolute -top-1 left-2 w-2 h-2 bg-gray-900 transform rotate-45"></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLayoutMode(layoutMode === 'horizontal' ? 'vertical' : 'horizontal')}
                  className="text-xs px-2 py-1"
                >
                  {layoutMode === 'horizontal' ? t('products.verticalLayout') : t('products.horizontalLayout')}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <CurrentStepComponent
              data={productData}
              onChange={setProductData}
              layoutMode={layoutMode}
              validationErrors={validationErrors}
              showValidation={showValidation}
              onStepClick={currentStep === 7 ? handleStepClick : undefined}
            />
          </CardContent>
          <div className="border-t border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === STEPS[0].id}
                className="flex items-center gap-1 text-sm px-3 py-2"
              >
                <ChevronLeft className="w-4 h-4" />
                {t('products.previous')}
              </Button>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handleSaveDraft}
                  className="flex items-center gap-1 text-sm px-3 py-2"
                  disabled={loading}
                >
                  <Save className="w-4 h-4" />
                  <span className="hidden sm:inline">{t('products.saveDraft')}</span>
                </Button>

                {currentStep === STEPS[STEPS.length - 1].id ? (
                  <Button
                    onClick={handleSubmitForApproval}
                    className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-sm px-3 py-2"
                    disabled={loading}
                  >
                    <Check className="w-4 h-4" />
                    {t('products.submitForApproval')}
                  </Button>
                ) : (
                  <Button
                    onClick={handleNext}
                    className="flex items-center gap-1 text-sm px-3 py-2"
                    disabled={loading}
                  >
                    {t('products.next')}
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Card>
    </div>
  );
};

export default ProductWizardPage;
