import { Formik, Form, FieldArray } from "formik";
import * as Yup from "yup";
import { FormInput } from "@/components/ui/form";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import { FaTrash, FaRegPlusSquare } from "react-icons/fa";

const dropDownForm = ({ dropdown, onSubmit, onCancel }) => {
  const { t } = useTranslation();

  const initialValues = dropdown
    ? {
        id: dropdown.id,
        name_en: dropdown.name_en || "",
        name_ar: dropdown.name_ar || "",
        status: dropdown.status || "inactive",
        slug: dropdown.slug || "",
        options: dropdown.options || [{ value_en: "", value_ar: "" }],
      }
    : {
        name_en: "",
        name_ar: "",
        status: "inactive",
        slug: "",
        options: [{ value_en: "", value_ar: "" }],
      };

  const validationSchema = Yup.object({
    name_en: Yup.string().required(t("commonValidation.name_en")),
    status: Yup.string().required(t("commonValidation.status")),
    options: Yup.array()
      .min(1, t("commonValidation.atLeastOneValue"))
      .of(
        Yup.object().shape({
          value_en: Yup.string().required(t("commonValidation.value_en")),
          value_ar: Yup.string(),
        })
      )
      .required(t("commonValidation.valuesRequired")),
  });

  const handleSubmit = (values, { setSubmitting }) => {
    onSubmit(values);
    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ isSubmitting, values }) => (
        <Form className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                name="name_en"
                label={t("commonField.name_en")}
                placeholder={t("commonPlaceholder.name_enPlaceholder")}
                required
              />
              <FormInput
                name="name_ar"
                label={t("commonField.name_ar")}
                placeholder={t("commonPlaceholder.name_arPlaceholder")}
              />
            </div>
          </div>

          {/* Values List */}
          <div className="bg-white p-6 rounded-lg shadow border">
            <FieldArray name="options">
              {({ push, remove }) => (
                <div className="space-y-6">
                  <div className="flex justify-between items-center border-b pb-2">
                    <h3 className="text-lg font-semibold text-gray-800">
                      {t("attributes.valuesTitle")}
                    </h3>
                    <Button
                      type="button"
                      variant="primary"
                      onClick={() => push({ value_en: "", value_ar: "" })}
                    >
                      <FaRegPlusSquare className="mx-2" />
                      {t("attributes.addValue")}
                    </Button>
                  </div>

                  {values.options.map((_, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-1 md:grid-cols-12 gap-4 items-end bg-gray-50 p-4 rounded-md border"
                    >
                      <div className="md:col-span-5 w-full">
                        <FormInput
                          name={`options[${index}].value_en`}
                          label={t("commonField.value_en")}
                          placeholder={t(
                            "commonPlaceholder.value_enPlaceholder"
                          )}
                          required
                        />
                      </div>
                      <div className="md:col-span-5 w-full">
                        <FormInput
                          name={`options[${index}].value_ar`}
                          label={t("commonField.value_ar")}
                          placeholder={t(
                            "commonPlaceholder.value_arPlaceholder"
                          )}
                        />
                      </div>
                      <div className="md:col-span-2 flex justify-end">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => remove(index)}
                          className="text-red-600"
                          disabled={values.options.length === 1}
                        >
                          <FaTrash className="mx-2" />
                          {t("commonButton.remove")}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </FieldArray>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end pt-6 border-t border-gray-200 mt-8 gap-3">
            <Button type="button" variant="outline" onClick={onCancel}>
              {t("commonButton.cancel")}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {dropdown
                ? t("commonButton.dropDown.updated")
                : t("commonButton.dropDown.create")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default dropDownForm;
