
import React, { useMemo, useCallback } from 'react';
import { Input } from '@/components/ui/product/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  AlertCircle,
  Heart,
  Utensils,
  Dumbbell,
  Scale,
  Pill,
  Stethoscope,
  Package,
  Activity,
  Baby,
  LucideIcon
} from 'lucide-react';

// Types
interface Category {
  id: string;
  name: string;
  icon: LucideIcon;
  color: string;
}

interface ProductData {
  category_id: string;
  sub_category_id: string;
  class_id: string;
  sub_class_id: string;
  brand_id: string;
  vendor_sku: string;
  barcode: string;
  model_number: string;
  system_sku: string;
}

interface StepProductDetailsProps {
  data: ProductData;
  onChange: (data: ProductData) => void;
  layoutMode: 'horizontal' | 'vertical';
  validationErrors?: Record<string, string>;
  showValidation?: boolean;
}

// Constants - moved outside component for better performance
const CATEGORIES: Category[] = [
  { id: 'vhms', name: 'VHMS', icon: Activity, color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  { id: 'beauty', name: 'Beauty', icon: Heart, color: 'bg-pink-50 border-pink-200 hover:bg-pink-100' },
  { id: 'food-drink', name: 'Food & Drink', icon: Utensils, color: 'bg-orange-50 border-orange-200 hover:bg-orange-100' },
  { id: 'sports-nutrition', name: 'Sports Nutrition', icon: Dumbbell, color: 'bg-green-50 border-green-200 hover:bg-green-100' },
  { id: 'weight-management', name: 'Weight Management', icon: Scale, color: 'bg-purple-50 border-purple-200 hover:bg-purple-100' },
  { id: 'mom-baby', name: 'Mom & Baby', icon: Baby, color: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100' },
  { id: 'para-pharmacy', name: 'Para Pharmacy', icon: Pill, color: 'bg-red-50 border-red-200 hover:bg-red-100' },
  { id: 'med-equipment', name: 'Med Equipment', icon: Stethoscope, color: 'bg-teal-50 border-teal-200 hover:bg-teal-100' },
  { id: 'misc-gen-items', name: 'Misc Gen Items', icon: Package, color: 'bg-gray-50 border-gray-200 hover:bg-gray-100' }
] as const;

// Optimized Category Button Component
const CategoryButton = React.memo(({
  category,
  isSelected,
  onSelect
}: {
  category: Category;
  isSelected: boolean;
  onSelect: (id: string) => void;
}) => {
  const IconComponent = category.icon;

  return (
    <button
      type="button"
      className={`
        p-3 rounded-lg border-2 transition-all duration-200 hover:shadow-md
        ${isSelected
          ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200 shadow-md'
          : 'border-gray-200 hover:border-gray-300 bg-white'
        }
      `}
      onClick={() => onSelect(category.id)}
    >
      <div className="flex flex-col items-center space-y-1">
        <div className={`
          transition-colors duration-200
          ${isSelected ? 'text-blue-600' : 'text-gray-600'}
        `}>
          <IconComponent className="w-5 h-5" />
        </div>
        <span className={`
          text-xs font-medium transition-colors duration-200
          ${isSelected ? 'text-blue-900' : 'text-gray-700'}
        `}>
          {category.name}
        </span>
      </div>
    </button>
  );
});

CategoryButton.displayName = 'CategoryButton';

// Hierarchical dropdown data structure
const hierarchicalData = {
  'vhms': {
    subCategories: [
      'Veterinary Software', 'Practice Management', 'Medical Records', 'Appointment Scheduling',
      'Billing Systems', 'Inventory Management', 'Laboratory Management', 'Imaging Systems',
      'Telemedicine', 'Mobile Apps', 'Cloud Solutions', 'Data Analytics',
      'Compliance Tools', 'Communication Systems', 'Emergency Management', 'Surgical Planning',
      'Pharmacy Management', 'Client Portals', 'Staff Management', 'Reporting Tools',
      'Integration Services', 'Training Modules', 'Support Systems', 'Backup Solutions'
    ],
    classes: {
      'Veterinary Software': ['Enterprise', 'Professional', 'Standard', 'Basic'],
      'Practice Management': ['Multi-location', 'Single Practice', 'Specialty', 'General'],
      'Medical Records': ['Electronic', 'Digital', 'Cloud-based', 'On-premise']
    },
    subClasses: {
      'Enterprise': ['Premium', 'Advanced', 'Standard'],
      'Professional': ['Plus', 'Standard', 'Lite'],
      'Multi-location': ['Unlimited', 'Up to 10', 'Up to 5']
    }
  },
  'beauty': {
    subCategories: [
      'Skincare', 'Makeup', 'Hair Care', 'Nail Care', 'Fragrance', 'Body Care',
      'Sun Care', 'Anti-Aging', 'Acne Treatment', 'Moisturizers', 'Cleansers', 'Serums',
      'Face Masks', 'Eye Care', 'Lip Care', 'Foundation', 'Concealer', 'Powder',
      'Blush', 'Bronzer', 'Eyeshadow', 'Mascara', 'Eyeliner', 'Lipstick'
    ],
    classes: {
      'Skincare': ['Luxury', 'Premium', 'Drugstore', 'Natural'],
      'Makeup': ['High-end', 'Mid-range', 'Budget', 'Professional'],
      'Hair Care': ['Salon', 'Professional', 'Consumer', 'Organic']
    },
    subClasses: {
      'Luxury': ['Ultra-premium', 'Premium', 'High-end'],
      'High-end': ['Designer', 'Professional', 'Premium'],
      'Salon': ['Professional', 'Retail', 'Treatment']
    }
  }
  // Add more categories as needed...
};

// Brand data organized by category
const brandsByCategory = {
  'vhms': ['VetSoft', 'PetDesk', 'eVetPractice', 'Cornerstone', 'IDEXX', 'Covetrus'],
  'beauty': ['L\'Oréal', 'Estée Lauder', 'Procter & Gamble', 'Unilever', 'Shiseido', 'Coty'],
  'food-drink': ['Nestlé', 'PepsiCo', 'Coca-Cola', 'Unilever', 'Kraft Heinz', 'General Mills'],
  'sports-nutrition': ['Optimum Nutrition', 'BSN', 'MuscleTech', 'Dymatize', 'Quest', 'Cellucor'],
  'weight-management': ['Weight Watchers', 'Nutrisystem', 'Jenny Craig', 'Medifast', 'SlimFast', 'Atkins'],
  'para-pharmacy': ['Johnson & Johnson', 'Pfizer', 'Roche', 'Novartis', 'Merck', 'GSK'],
  'med-equipment': ['Medtronic', 'Abbott', 'Boston Scientific', 'Stryker', 'Zimmer Biomet', 'Edwards'],
  'misc-gen-items': ['3M', 'Honeywell', 'General Electric', 'Siemens', 'Bosch', 'Philips']
};

export default function StepProductDetails({
  data,
  onChange,
  layoutMode,
  validationErrors,
  showValidation = false
}: StepProductDetailsProps) {
  // Optimized handlers with useCallback to prevent unnecessary re-renders
  const handleChange = useCallback((field: string, value: any) => {
    onChange({ ...data, [field]: value });
  }, [data, onChange]);

  const handleCategorySelect = useCallback((categoryId: string) => {
    // Reset dependent fields when category changes
    onChange({
      ...data,
      category_id: categoryId,
      sub_category_id: '',
      class_id: '',
      sub_class_id: '',
      brand_id: ''
    });
  }, [data, onChange]);

  const handleSubCategoryChange = useCallback((subCategoryId: string) => {
    // Reset dependent fields when sub-category changes
    onChange({
      ...data,
      sub_category_id: subCategoryId,
      class_id: '',
      sub_class_id: ''
    });
  }, [data, onChange]);

  const handleClassChange = useCallback((classId: string) => {
    // Reset sub-class when class changes
    onChange({
      ...data,
      class_id: classId,
      sub_class_id: ''
    });
  }, [data, onChange]);

  // Memoized data fetchers for better performance
  const subCategories = useMemo(() => {
    if (!data.category_id || !hierarchicalData[data.category_id]) return [];
    return hierarchicalData[data.category_id].subCategories || [];
  }, [data.category_id]);

  const classes = useMemo(() => {
    if (!data.category_id || !data.sub_category_id || !hierarchicalData[data.category_id]) return [];
    return hierarchicalData[data.category_id].classes[data.sub_category_id] || [];
  }, [data.category_id, data.sub_category_id]);

  const subClasses = useMemo(() => {
    if (!data.category_id || !data.class_id || !hierarchicalData[data.category_id]) return [];
    return hierarchicalData[data.category_id].subClasses[data.class_id] || [];
  }, [data.category_id, data.class_id]);

  const brands = useMemo(() => {
    if (!data.category_id || !brandsByCategory[data.category_id]) return [];
    return brandsByCategory[data.category_id] || [];
  }, [data.category_id]);

  // Memoized validation
  const validation = useMemo(() => {
    const isCategoryValid = Boolean(data.category_id);
    const isSubCategoryValid = Boolean(data.sub_category_id);
    const isBrandValid = Boolean(data.brand_id);
    const isBarcodeValid = Boolean(data.barcode);

    const allMandatoryFieldsValid = isCategoryValid && isSubCategoryValid && isBrandValid && isBarcodeValid;
    const shouldShowValidationHint = showValidation && !allMandatoryFieldsValid;

    const missingFields = [];
    if (!isCategoryValid) missingFields.push('Product Category');
    if (!isSubCategoryValid) missingFields.push('Sub Category');
    if (!isBrandValid) missingFields.push('Brand Name');
    if (!isBarcodeValid) missingFields.push('Barcode');

    return {
      isCategoryValid,
      isSubCategoryValid,
      isBrandValid,
      isBarcodeValid,
      allMandatoryFieldsValid,
      shouldShowValidationHint,
      missingFields
    };
  }, [data.category_id, data.sub_category_id, data.brand_id, data.barcode, showValidation]);



  return (
    <div className="space-y-6">
      {/* Compact Category Selection */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-gray-900">Product Category</h3>

        {/* Single Row Category Grid - Optimized */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-2">
          {CATEGORIES.map((category) => (
            <CategoryButton
              key={category.id}
              category={category}
              isSelected={data.category_id === category.id}
              onSelect={handleCategorySelect}
            />
          ))}
        </div>
      </div>

      {/* Hierarchical Dropdown System */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="space-y-2">
          <Label htmlFor="sub_category">Sub Category *</Label>
          <Select
            value={data.sub_category_id}
            onValueChange={handleSubCategoryChange}
            disabled={!data.category_id}
          >
            <SelectTrigger>
              <SelectValue placeholder={data.category_id ? "Select sub category" : "Select category first"} />
            </SelectTrigger>
            <SelectContent>
              {subCategories.map((subCategory) => (
                <SelectItem key={subCategory} value={subCategory}>
                  {subCategory}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="class">Class *</Label>
          <Select
            value={data.class_id}
            onValueChange={handleClassChange}
            disabled={!data.sub_category_id}
          >
            <SelectTrigger>
              <SelectValue placeholder={data.sub_category_id ? "Select class" : "Select sub category first"} />
            </SelectTrigger>
            <SelectContent>
              {classes.map((classItem) => (
                <SelectItem key={classItem} value={classItem}>
                  {classItem}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="sub_class">Sub Class (Optional)</Label>
          <Select
            value={data.sub_class_id}
            onValueChange={(value) => handleChange('sub_class_id', value)}
            disabled={!data.class_id}
          >
            <SelectTrigger>
              <SelectValue placeholder={data.class_id ? "Select sub class" : "Select class first"} />
            </SelectTrigger>
            <SelectContent>
              {subClasses.map((subClass) => (
                <SelectItem key={subClass} value={subClass}>
                  {subClass}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="brand">Brand *</Label>
          <Select
            value={data.brand_id}
            onValueChange={(value) => handleChange('brand_id', value)}
            disabled={!data.category_id}
          >
            <SelectTrigger>
              <SelectValue placeholder={data.category_id ? "Select brand" : "Select category first"} />
            </SelectTrigger>
            <SelectContent>
              {brands.map((brand) => (
                <SelectItem key={brand} value={brand.toLowerCase().replace(/\s+/g, '-')}>
                  {brand}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h4 className="font-semibold text-yellow-800 mb-3">Product Code Configuration</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="vendor_sku">Product Code (SKU)</Label>
            <Input
              id="vendor_sku"
              value={data.vendor_sku}
              onChange={(e) => handleChange('vendor_sku', e.target.value)}
              placeholder="Enter your unique product code"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="barcode">Barcode *</Label>
            <Input
              id="barcode"
              value={data.barcode}
              onChange={(e) => handleChange('barcode', e.target.value)}
              placeholder="Product barcode"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="model_number">Model Number</Label>
            <Input
              id="model_number"
              value={data.model_number}
              onChange={(e) => handleChange('model_number', e.target.value)}
              placeholder="Product model number"
            />
          </div>
        </div>

        {validation.shouldShowValidationHint && (
          <div
            className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200 shadow-sm"
            role="alert"
            aria-live="polite"
            aria-describedby="product-code-validation-message"
          >
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="text-sm font-semibold text-red-800 mb-1">
                  Validation Error
                </h5>
                <p
                  id="product-code-validation-message"
                  className="text-sm text-red-700"
                >
                  Please fill in the following required fields: {validation.missingFields.join(', ')}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {data.system_sku && (
        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <Label className="text-sm font-medium text-green-800">Auto-generated System SKU</Label>
          <p className="text-green-700 font-mono mt-1">{data.system_sku}</p>
        </div>
      )}
    </div>
  );
}
