import React, { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useState } from "react";
import { Input } from "@/components/ui/product/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertCircle,
  Heart,
  Utensils,
  Dumbbell,
  Scale,
  Pill,
  Stethoscope,
  Package,
  Activity,
  Baby,
  LucideIcon,
  Loader2,
} from "lucide-react";
import { fetchData } from "@/hooks/useApi";
import { useDebounce } from "use-debounce";

// Types
interface Category {
  id: number;
  name: string;
  icon: LucideIcon;
  color: string;
}

interface SubCategory {
  id: number;
  name: string;
  category_id: number;
}

interface ProductClass {
  id: number;
  name: string;
}

interface SubClass {
  id: number;
  name: string;
  class_id: number;
}

interface Brand {
  id: number;
  name: string;
}

interface ProductData {
  category_id: string | number;
  sub_category_id: string | number;
  class_id: string | number;
  sub_class_id: string | number;
  brand_id: string | number;
  vendor_sku: string;
  barcode: string;
  model_number: string;
  system_sku: string;
}

interface StepProductDetailsProps {
  data: ProductData;
  onChange: (data: ProductData) => void;
  layoutMode: "horizontal" | "vertical";
  validationErrors?: Record<string, string>;
  showValidation?: boolean;
}

// Icon mapping for categories - fallback for when API doesn't provide icons
const CATEGORY_ICONS: Record<string, LucideIcon> = {
  "VHMS": Activity,
  "Beauty": Heart,
  "Food & Drink": Utensils,
  "Sports Nutrition": Dumbbell,
  "Weight Management": Scale,
  "Mom & Baby": Baby,
  "Para Pharmacy": Pill,
  "Med Equipment": Stethoscope,
  "Misc Gen Items": Package,
} as const;

// Color mapping for categories - fallback for when API doesn't provide colors
const CATEGORY_COLORS: Record<string, string> = {
  "VHMS": "bg-blue-50 border-blue-200 hover:bg-blue-100",
  "Beauty": "bg-pink-50 border-pink-200 hover:bg-pink-100",
  "Food & Drink": "bg-orange-50 border-orange-200 hover:bg-orange-100",
  "Sports Nutrition": "bg-green-50 border-green-200 hover:bg-green-100",
  "Weight Management": "bg-purple-50 border-purple-200 hover:bg-purple-100",
  "Mom & Baby": "bg-yellow-50 border-yellow-200 hover:bg-yellow-100",
  "Para Pharmacy": "bg-red-50 border-red-200 hover:bg-red-100",
  "Med Equipment": "bg-teal-50 border-teal-200 hover:bg-teal-100",
  "Misc Gen Items": "bg-gray-50 border-gray-200 hover:bg-gray-100",
} as const;

// Optimized Category Button Component
const CategoryButton = React.memo(
  ({
    category,
    isSelected,
    onSelect,
  }: {
    category: Category;
    isSelected: boolean;
    onSelect: (id: number) => void;
  }) => {
    const IconComponent = category.icon || Package; // Fallback icon

    return (
      <button
        type="button"
        className={`
        p-3 rounded-lg border-2 transition-all duration-200 hover:shadow-md
        ${
          isSelected
            ? "ring-2 ring-blue-500 bg-blue-50 border-blue-200 shadow-md"
            : "border-gray-200 hover:border-gray-300 bg-white"
        }
      `}
        onClick={() => onSelect(category.id)}
      >
        <div className="flex flex-col items-center space-y-1">
          <div
            className={`
          transition-colors duration-200
          ${isSelected ? "text-blue-600" : "text-gray-600"}
        `}
          >
            <IconComponent className="w-5 h-5" />
          </div>
          <span
            className={`
          text-xs font-medium transition-colors duration-200
          ${isSelected ? "text-blue-900" : "text-gray-700"}
        `}
          >
            {category.name}
          </span>
        </div>
      </button>
    );
  }
);

CategoryButton.displayName = "CategoryButton";

// Helper function to get icon for category
const getCategoryIcon = (categoryName: string): LucideIcon => {
  return CATEGORY_ICONS[categoryName] || Package;
};

// Helper function to get color for category
const getCategoryColor = (categoryName: string): string => {
  return CATEGORY_COLORS[categoryName] || "bg-gray-50 border-gray-200 hover:bg-gray-100";
};

export default function StepProductDetails({
  data,
  onChange,
  layoutMode,
  validationErrors,
  showValidation = false,
}: StepProductDetailsProps) {
  // Debounced values for API calls to avoid excessive requests
  const [debouncedCategoryId] = useDebounce(data.category_id, 300);
  const [debouncedClassId] = useDebounce(data.class_id, 300);

  // API calls for fetching data
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = fetchData("general/categories/active-list", {}, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  const {
    data: subCategoriesData,
    isLoading: subCategoriesLoading,
    error: subCategoriesError,
  } = fetchData(
    debouncedCategoryId ? `general/sub-categories/by-category/${debouncedCategoryId}` : "",
    {},
    {
      enabled: !!debouncedCategoryId,
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    }
  );

  const {
    data: classesData,
    isLoading: classesLoading,
    error: classesError,
  } = fetchData("general/classes/active-list", {}, {
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });

  const {
    data: subClassesData,
    isLoading: subClassesLoading,
    error: subClassesError,
  } = fetchData(
    debouncedClassId ? `general/sub-classes/by-class/${debouncedClassId}` : "",
    {},
    {
      enabled: !!debouncedClassId,
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    }
  );

  const {
    data: brandsData,
    isLoading: brandsLoading,
    error: brandsError,
  } = fetchData("general/brands/active-list", {}, {
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });

  // Optimized handlers with useCallback to prevent unnecessary re-renders
  const handleChange = useCallback(
    (field: string, value: any) => {
      onChange({ ...data, [field]: value });
    },
    [data, onChange]
  );

  const handleCategorySelect = useCallback(
    (categoryId: number) => {
      // Reset dependent fields when category changes
      onChange({
        ...data,
        category_id: categoryId,
        sub_category_id: "",
        class_id: "",
        sub_class_id: "",
        brand_id: "",
      });
    },
    [data, onChange]
  );

  const handleSubCategoryChange = useCallback(
    (subCategoryId: string) => {
      // Reset dependent fields when sub-category changes
      onChange({
        ...data,
        sub_category_id: subCategoryId,
        class_id: "",
        sub_class_id: "",
      });
    },
    [data, onChange]
  );

  const handleClassChange = useCallback(
    (classId: string) => {
      // Reset sub-class when class changes
      onChange({
        ...data,
        class_id: classId,
        sub_class_id: "",
      });
    },
    [data, onChange]
  );

  // Memoized data processing for better performance
  const categories = useMemo(() => {
    if (!categoriesData?.data) return [];
    return categoriesData.data.map((cat: any) => ({
      id: cat.id,
      name: cat.name,
      icon: getCategoryIcon(cat.name),
      color: getCategoryColor(cat.name),
    }));
  }, [categoriesData]);

  const subCategories = useMemo(() => {
    if (!subCategoriesData?.data) return [];
    return subCategoriesData.data;
  }, [subCategoriesData]);

  const classes = useMemo(() => {
    if (!classesData?.data) return [];
    return classesData.data;
  }, [classesData]);

  const subClasses = useMemo(() => {
    if (!subClassesData?.data) return [];
    return subClassesData.data;
  }, [subClassesData]);

  const brands = useMemo(() => {
    if (!brandsData?.data) return [];
    return brandsData.data;
  }, [brandsData]);

  // Memoized validation
  const validation = useMemo(() => {
    const isCategoryValid = Boolean(data.category_id);
    const isSubCategoryValid = Boolean(data.sub_category_id);
    const isBrandValid = Boolean(data.brand_id);
    const isBarcodeValid = Boolean(data.barcode);

    const allMandatoryFieldsValid =
      isCategoryValid && isSubCategoryValid && isBrandValid && isBarcodeValid;
    const shouldShowValidationHint = showValidation && !allMandatoryFieldsValid;

    const missingFields = [];
    if (!isCategoryValid) missingFields.push("Product Category");
    if (!isSubCategoryValid) missingFields.push("Sub Category");
    if (!isBrandValid) missingFields.push("Brand Name");
    if (!isBarcodeValid) missingFields.push("Barcode");

    return {
      isCategoryValid,
      isSubCategoryValid,
      isBrandValid,
      isBarcodeValid,
      allMandatoryFieldsValid,
      shouldShowValidationHint,
      missingFields,
    };
  }, [
    data.category_id,
    data.sub_category_id,
    data.brand_id,
    data.barcode,
    showValidation,
  ]);

  return (
    <div className="space-y-6">
      {/* Compact Category Selection */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-gray-900">
          Product Category
        </h3>

        {categoriesLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading categories...</span>
          </div>
        ) : categoriesError ? (
          <div className="p-4 bg-red-50 rounded-lg border border-red-200">
            <p className="text-red-600">Failed to load categories. Please try again.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-9 gap-2">
            {categories.map((category) => (
              <CategoryButton
                key={category.id}
                category={category}
                isSelected={Number(data.category_id) === category.id}
                onSelect={handleCategorySelect}
              />
            ))}
          </div>
        )}
      </div>

      {/* Hierarchical Dropdown System */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="space-y-2">
          <Label htmlFor="sub_category">Sub Category *</Label>
          <Select
            value={String(data.sub_category_id)}
            onValueChange={handleSubCategoryChange}
            disabled={!data.category_id || subCategoriesLoading}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={
                  subCategoriesLoading
                    ? "Loading..."
                    : data.category_id
                    ? "Select sub category"
                    : "Select category first"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {subCategories.map((subCategory: SubCategory) => (
                <SelectItem key={subCategory.id} value={String(subCategory.id)}>
                  {subCategory.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {subCategoriesError && (
            <p className="text-xs text-red-600">Failed to load sub categories</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="class">Class *</Label>
          <Select
            value={String(data.class_id)}
            onValueChange={handleClassChange}
            disabled={classesLoading}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={
                  classesLoading ? "Loading..." : "Select class"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {classes.map((classItem: ProductClass) => (
                <SelectItem key={classItem.id} value={String(classItem.id)}>
                  {classItem.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {classesError && (
            <p className="text-xs text-red-600">Failed to load classes</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="sub_class">Sub Class (Optional)</Label>
          <Select
            value={String(data.sub_class_id)}
            onValueChange={(value) => handleChange("sub_class_id", value)}
            disabled={!data.class_id || subClassesLoading}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={
                  subClassesLoading
                    ? "Loading..."
                    : data.class_id
                    ? "Select sub class"
                    : "Select class first"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {subClasses.map((subClass: SubClass) => (
                <SelectItem key={subClass.id} value={String(subClass.id)}>
                  {subClass.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {subClassesError && (
            <p className="text-xs text-red-600">Failed to load sub classes</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="brand">Brand *</Label>
          <Select
            value={String(data.brand_id)}
            onValueChange={(value) => handleChange("brand_id", value)}
            disabled={brandsLoading}
          >
            <SelectTrigger>
              <SelectValue
                placeholder={
                  brandsLoading ? "Loading..." : "Select brand"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {brands.map((brand: Brand) => (
                <SelectItem key={brand.id} value={String(brand.id)}>
                  {brand.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {brandsError && (
            <p className="text-xs text-red-600">Failed to load brands</p>
          )}
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h4 className="font-semibold text-yellow-800 mb-3">
          Product Code Configuration
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="vendor_sku">Product Code (SKU)</Label>
            <Input
              id="vendor_sku"
              value={data.vendor_sku}
              onChange={(e) => handleChange("vendor_sku", e.target.value)}
              placeholder="Enter your unique product code"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="barcode">Barcode *</Label>
            <Input
              id="barcode"
              value={data.barcode}
              onChange={(e) => handleChange("barcode", e.target.value)}
              placeholder="Product barcode"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="model_number">Model Number</Label>
            <Input
              id="model_number"
              value={data.model_number}
              onChange={(e) => handleChange("model_number", e.target.value)}
              placeholder="Product model number"
            />
          </div>
        </div>

        {validation.shouldShowValidationHint && (
          <div
            className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200 shadow-sm"
            role="alert"
            aria-live="polite"
            aria-describedby="product-code-validation-message"
          >
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="text-sm font-semibold text-red-800 mb-1">
                  Validation Error
                </h5>
                <p
                  id="product-code-validation-message"
                  className="text-sm text-red-700"
                >
                  Please fill in the following required fields:{" "}
                  {validation.missingFields.join(", ")}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {data.system_sku && (
        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <Label className="text-sm font-medium text-green-800">
            Auto-generated System SKU
          </Label>
          <p className="text-green-700 font-mono mt-1">{data.system_sku}</p>
        </div>
      )}
    </div>
  );
}
